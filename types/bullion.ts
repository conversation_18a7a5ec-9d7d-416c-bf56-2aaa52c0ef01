export interface CommodityPrice {
  id: string
  name: string
  weight: string
  buyPrice: number
  sellPrice: number
  change?: number
  changePercent?: number
  lastUpdated?: Date
}

export interface LivePrice {
  symbol: string
  buyPrice: number
  sellPrice: number
  change: number
  changePercent: number
  trend: 'up' | 'down' | 'neutral'
  lastUpdated?: Date
}

export interface MarketStatus {
  isOpen: boolean
  nextOpen?: Date
  nextClose?: Date
  timezone: string
}

export interface NewsItem {
  id: string
  title: string
  content: string
  timestamp: Date
  source?: string
}

export interface BullionBoardData {
  commodities: CommodityPrice[]
  livePrices: {
    goldOz: LivePrice
    silverOz: LivePrice
  }
  marketStatus: MarketStatus
  news: NewsItem[]
  lastUpdated: Date
}

export type PriceChangeDirection = 'up' | 'down' | 'neutral'

export interface PriceDisplayProps {
  price: number
  change?: number
  changePercent?: number
  trend?: PriceChangeDirection
  currency?: string
  precision?: number
}
