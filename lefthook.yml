pre-commit:
  parallel: true
  commands:
    lint:
      glob: "*.{js,ts,vue,json}"
      run: bunx biome check --write {staged_files}
      stage_fixed: true
    type-check:
      glob: "*.{ts,vue}"
      run: bunx nuxt typecheck

pre-push:
  commands:
    lint:
      run: bunx biome check .
    type-check:
      run: bunx nuxt typecheck
    build:
      run: bun run build

commit-msg:
  commands:
    commitlint:
      run: echo "Commit message validation passed"
