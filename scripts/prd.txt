# Digital Bullion Board - Product Requirements Document

## Project Overview
Create a real-time digital bullion trading board interface using Nuxt 3, TypeScript, and modern UI components that replicates the SARCO Jewellery bullion display system shown in the provided image.

## Business Objectives
- Provide real-time bullion price display for gold and silver commodities
- Create an intuitive trading interface for jewelry businesses
- Display market status and price changes with visual indicators
- Support responsive design for multiple device types

## Technical Requirements

### Framework & Technology Stack
- **Frontend Framework**: Nuxt 3 with TypeScript
- **UI Components**: Shadcn/UI and Magic UI components
- **Styling**: Tailwind CSS
- **State Management**: Pinia for real-time data management
- **Icons**: Lucide Icons
- **Typography**: Inter font family
- **Real-time Updates**: WebSocket or polling mechanism

### Core Features

#### 1. Header Section
- Company branding display ("SARCO JEWELLERY" logo)
- Real-time date and time display (format: "TUESDAY 29 APR 2025 07:11 PM")
- Market status indicators with visual badges

#### 2. Commodity Trading Table
- Display commodity types: GOLD 995, GOLD 22K, 1 GM, GOLD TEN TOLA, SILVER
- Show weight units (1KG, 1GM, 1TTB)
- Real-time BUY and SELL prices
- Price change animations (green for increase, red for decrease)

#### 3. Live Price Display Panel
- Gold OZ section with main prices and change indicators
- Silver OZ section with current rates
- Visual trend indicators (up/down arrows)
- Color-coded price changes

#### 4. Market News Ticker
- Scrolling news feed at bottom
- Display relevant bullion market news
- Website attribution display

### User Interface Requirements

#### Design System
- Dark theme with professional appearance
- Color scheme: Dark slate background (#0f172a), blue accents (#3b82f6)
- Success/danger colors for price movements (green/red)
- Clean typography hierarchy

#### Responsive Design
- Mobile: Stacked vertical layout
- Tablet: Adjusted side-by-side layout
- Desktop: Full horizontal layout as shown in reference

#### Animation & Interactions
- Smooth price update transitions
- Flash animations for price changes
- Hover effects on interactive elements
- Loading skeleton states

### Data Structure Requirements

#### Commodity Price Model
- Commodity ID and name
- Weight specification
- Buy/sell prices with decimal precision
- Change amount and percentage
- Timestamp for last update

#### Live Price Model
- Symbol identification
- Current buy/sell rates
- Change indicators with trend direction
- Market status information

### Performance Requirements
- Real-time price updates (1-5 second intervals)
- Smooth animations without performance degradation
- Fast initial page load (<3 seconds)
- Responsive UI interactions (<100ms)

### Component Architecture

#### Core Components
1. **BullionBoard.vue** - Main container component
2. **CommodityTable.vue** - Trading table with price listings
3. **LivePrices.vue** - Real-time price display panel
4. **PriceCard.vue** - Individual price display cards
5. **MarketTicker.vue** - Scrolling news ticker
6. **StatusIndicator.vue** - Market status badges
7. **HeaderSection.vue** - Top navigation and branding

#### Utility Components
- Loading skeletons for data fetching states
- Error boundary components
- Price change animation components

### Integration Requirements
- WebSocket connection for real-time data
- REST API endpoints for historical data
- Error handling for network failures
- Offline state management

### Testing Requirements
- Unit tests for all components
- Integration tests for real-time data flow
- E2E tests for user interactions
- Performance testing for real-time updates

### Deployment Requirements
- Production build optimization
- CDN integration for static assets
- Environment configuration management
- Monitoring and analytics integration

## Success Criteria
- Accurate real-time price display matching reference design
- Smooth user experience across all device types
- Reliable real-time data updates
- Professional appearance suitable for business use
- Fast loading and responsive performance

## Future Enhancements
- Historical price charts
- Price alert notifications
- Export functionality (CSV/PDF)
- Multi-language support
- Theme customization options
- Advanced filtering and search capabilities
