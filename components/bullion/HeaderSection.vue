<template>
  <header class="bg-slate-900 border-b border-slate-700">
    <div class="container mx-auto px-4 py-3">
      <div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
        <!-- Company branding -->
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <Icon name="lucide:coins" class="h-6 w-6 text-blue-400" />
            <h1 class="text-xl font-bold text-white">{{ companyName }}</h1>
          </div>
          <UiSeparator orientation="vertical" class="h-6 mx-2 hidden md:block" />
          <div class="hidden md:block">
            <UiBadge :variant="getStatusVariant(marketStatus.status)">
              <template #icon>
                <Icon :name="getStatusIcon(marketStatus.status)" class="h-3 w-3" />
              </template>
              {{ getStatusText(marketStatus.status) }}
            </UiBadge>
          </div>
        </div>

        <!-- Date and time display -->
        <div class="flex items-center gap-2 text-sm text-slate-300">
          <div class="flex items-center gap-1.5">
            <Icon name="lucide:clock" class="h-4 w-4 text-slate-400" />
            <span class="font-medium text-white">{{ formattedTime }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <span>{{ formattedDate }}</span>
        </div>
      </div>

      <!-- Market indicators -->
      <div class="mt-3 grid grid-cols-1 gap-2 sm:grid-cols-3 md:mt-2">
        <div
          v-for="indicator in indicators"
          :key="indicator.name"
          class="flex items-center justify-between rounded-md border border-slate-600 bg-slate-800/50 px-3 py-1.5"
        >
          <span class="text-sm font-medium text-slate-200">{{ indicator.name }}</span>
          <div class="flex items-center gap-2">
            <span class="font-semibold text-white">{{ indicator.value }}</span>
            <span
              :class="[
                'flex items-center text-xs font-medium',
                getChangeColorClass(indicator.changeType)
              ]"
            >
              <Icon
                v-if="indicator.changeType !== 'neutral'"
                :name="indicator.changeType === 'positive' ? 'lucide:trending-up' : 'lucide:trending-down'"
                class="h-3 w-3 mr-0.5"
              />
              {{ indicator.change }}
            </span>
          </div>
        </div>
      </div>

      <!-- Mobile status badge -->
      <div class="mt-2 md:hidden">
        <Badge :variant="getStatusVariant(marketStatus.status)">
          <template #icon>
            <Icon :name="getStatusIcon(marketStatus.status)" class="h-3 w-3" />
          </template>
          {{ getStatusText(marketStatus.status) }}
        </Badge>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import type { MarketStatus } from '~/types/bullion'

interface MarketIndicator {
  name: string
  value: string
  change: string
  changeType: 'positive' | 'negative' | 'neutral'
}

interface Props {
  companyName?: string
  marketStatus?: MarketStatus
  indicators?: MarketIndicator[]
}

const props = withDefaults(defineProps<Props>(), {
  companyName: 'SARCO JEWELLERY',
  marketStatus: () => ({ isOpen: true, timezone: 'UTC' }),
  indicators: () => [
    {
      name: 'Gold',
      value: '₹3310.77',
      change: '****%',
      changeType: 'positive'
    },
    {
      name: 'Silver',
      value: '33.585',
      change: '-0.5%',
      changeType: 'negative'
    },
    {
      name: 'Platinum',
      value: '₹1045.30',
      change: '+0.8%',
      changeType: 'positive'
    }
  ]
})

// Real-time date and time
const currentDateTime = ref(new Date())

const formattedDate = computed(() => {
  return currentDateTime.value.toLocaleDateString('en-US', {
    weekday: 'long',
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  }).toUpperCase()
})

const formattedTime = computed(() => {
  return currentDateTime.value.toLocaleTimeString('en-US', {
    hour12: true,
    hour: '2-digit',
    minute: '2-digit'
  })
})

// Update time every second
onMounted(() => {
  const timer = setInterval(() => {
    currentDateTime.value = new Date()
  }, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})

// Helper functions
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'open':
    case true:
      return 'success'
    case 'closed':
    case false:
      return 'secondary'
    case 'suspended':
      return 'warning'
    default:
      return 'default'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'open':
    case true:
      return 'lucide:check-circle'
    case 'closed':
    case false:
      return 'lucide:clock'
    case 'suspended':
      return 'lucide:alert-circle'
    default:
      return 'lucide:circle'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'open':
    case true:
      return 'Market Open'
    case 'closed':
    case false:
      return 'Market Closed'
    case 'suspended':
      return 'Trading Suspended'
    default:
      return 'Unknown'
  }
}

const getChangeColorClass = (changeType: string) => {
  switch (changeType) {
    case 'positive':
      return 'text-green-400'
    case 'negative':
      return 'text-red-400'
    default:
      return 'text-slate-400'
  }
}
</script>
