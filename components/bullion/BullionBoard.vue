<template>
  <div class="min-h-screen bg-slate-900 text-white">
    <!-- Header Section -->
    <BullionHeaderSection
      :company-name="'SARCO JEWELLERY'"
      :market-status="marketStatus"
      :indicators="marketIndicators"
    />
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Commodity Trading Table (Left Panel) -->
        <div class="lg:col-span-2">
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h2 class="text-lg font-semibold mb-4 text-blue-400">Commodity Trading</h2>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-slate-600">
                    <th class="text-left py-2 px-3 text-blue-400 font-medium">COMMODITY</th>
                    <th class="text-left py-2 px-3 text-blue-400 font-medium">WEIGHT</th>
                    <th class="text-right py-2 px-3 text-blue-400 font-medium">BUY</th>
                    <th class="text-right py-2 px-3 text-blue-400 font-medium">SELL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="commodity in commodities"
                    :key="commodity.id"
                    class="border-b border-slate-700 hover:bg-slate-700/50"
                  >
                    <td class="py-3 px-3 font-medium">{{ commodity.name }}</td>
                    <td class="py-3 px-3 text-slate-300">{{ commodity.weight }}</td>
                    <td class="py-3 px-3 text-right font-mono">{{ formatPrice(commodity.buyPrice) }}</td>
                    <td class="py-3 px-3 text-right font-mono">{{ formatPrice(commodity.sellPrice) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Live Prices Panel (Right Panel) -->
        <div class="space-y-4">
          <!-- Gold OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-sm font-medium text-blue-400 mb-3">Gold OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-2xl font-bold text-red-400">{{ livePrices.goldOz.buyPrice }}</span>
                <span class="text-2xl font-bold text-red-400">{{ livePrices.goldOz.sellPrice }}</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-3 w-3" />
                  +3348.57
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-3 w-3" />
                  -3299.65
                </span>
              </div>
            </div>
          </div>

          <!-- Silver OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-sm font-medium text-blue-400 mb-3">Silver OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-xl font-bold">{{ livePrices.silverOz.buyPrice }}</span>
                <span class="text-xl font-bold">{{ livePrices.silverOz.sellPrice }}</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-3 w-3" />
                  +33.502
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-3 w-3" />
                  -32.869
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer Ticker -->
    <footer class="bg-slate-800 border-t border-slate-700 py-2">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between text-sm">
          <div class="flex items-center gap-2">
            <span class="text-blue-400">Gold Price News:</span>
            <span class="text-slate-300">Borrowing to Trade Gold</span>
          </div>
          <span class="text-slate-400">www.bullionstats.com</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import type { CommodityPrice, LivePrice, MarketStatus } from '~/types/bullion'

// Mock data - in real app this would come from API/store
const marketStatus = ref<MarketStatus>({
  isOpen: true,
  timezone: 'UTC'
})

const marketIndicators = ref([
  {
    name: 'Gold',
    value: '₹3310.77',
    change: '+1.2%',
    changeType: 'positive' as const
  },
  {
    name: 'Silver',
    value: '33.585',
    change: '-0.5%',
    changeType: 'negative' as const
  }
])

const commodities = ref<CommodityPrice[]>([
  {
    id: '1',
    name: 'GOLD 995',
    weight: '1KG',
    buyPrice: 387943,
    sellPrice: 389962
  },
  {
    id: '2',
    name: 'GOLD 22K',
    weight: '1KG',
    buyPrice: 358701,
    sellPrice: 360568
  },
  {
    id: '3',
    name: '1 GM',
    weight: '1GM',
    buyPrice: 388.71,
    sellPrice: 393.69
  },
  {
    id: '4',
    name: 'GOLD TEN TOLA',
    weight: '1TTB',
    buyPrice: 45500,
    sellPrice: 45668
  },
  {
    id: '5',
    name: 'SILVER',
    weight: '1KG',
    buyPrice: 3727.69,
    sellPrice: 4203.36
  }
])

const livePrices = ref({
  goldOz: {
    symbol: 'GOLD_OZ',
    buyPrice: 3310.77,
    sellPrice: 3312.95,
    change: 1.2,
    changePercent: 0.036,
    trend: 'up' as const
  },
  silverOz: {
    symbol: 'SILVER_OZ',
    buyPrice: 33.585,
    sellPrice: 33.558,
    change: -0.5,
    changePercent: -0.015,
    trend: 'down' as const
  }
})

// Helper function to format prices
const formatPrice = (price: number): string => {
  if (price >= 1000) {
    return price.toLocaleString('en-IN')
  }
  return price.toFixed(2)
}
</script>
