// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: [
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/google-fonts'
  ],

  // Google Fonts configuration
  googleFonts: {
    families: {
      Inter: [400, 500, 600, 700]
    }
  },

  // Tailwind CSS configuration
  tailwindcss: {
    exposeConfig: true
  },

  // TypeScript configuration
  typescript: {
    typeCheck: false
  },

  // CSS configuration
  css: ['~/assets/css/main.css']
})