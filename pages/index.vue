<template>
  <div class="min-h-screen bg-slate-900 text-white">
    <!-- Header Section -->
    <header class="bg-slate-900 border-b border-slate-700">
      <div class="container mx-auto px-4 py-3">
        <div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
          <!-- Company branding -->
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2">
              <Icon name="lucide:coins" class="h-6 w-6 text-blue-400" />
              <h1 class="text-xl font-bold text-white">SARCO JEWELLERY</h1>
            </div>
            <div class="h-6 w-[1px] bg-slate-700 mx-2 hidden md:block" />
            <div class="hidden md:block">
              <span class="inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                <Icon name="lucide:check-circle" class="h-3 w-3" />
                Market Open
              </span>
            </div>
          </div>

          <!-- Date and time display -->
          <div class="flex items-center gap-2 text-sm text-slate-300">
            <div class="flex items-center gap-1.5">
              <Icon name="lucide:clock" class="h-4 w-4 text-slate-400" />
              <span class="font-medium text-white">{{ formattedTime }}</span>
            </div>
            <div class="h-4 w-[1px] bg-slate-700" />
            <span>{{ formattedDate }}</span>
          </div>
        </div>

        <!-- Market indicators -->
        <div class="mt-3 grid grid-cols-1 gap-2 sm:grid-cols-2 md:mt-2">
          <div class="flex items-center justify-between rounded-md border border-slate-600 bg-slate-800/50 px-3 py-1.5">
            <span class="text-sm font-medium text-slate-200">Gold</span>
            <div class="flex items-center gap-2">
              <span class="font-semibold text-white">₹3310.77</span>
              <span class="flex items-center text-xs font-medium text-green-400">
                <Icon name="lucide:trending-up" class="h-3 w-3 mr-0.5" />
                +1.2%
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between rounded-md border border-slate-600 bg-slate-800/50 px-3 py-1.5">
            <span class="text-sm font-medium text-slate-200">Silver</span>
            <div class="flex items-center gap-2">
              <span class="font-semibold text-white">33.585</span>
              <span class="flex items-center text-xs font-medium text-red-400">
                <Icon name="lucide:trending-down" class="h-3 w-3 mr-0.5" />
                -0.5%
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Commodity Trading Table (Left Panel) -->
        <div class="lg:col-span-2">
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h2 class="text-lg font-semibold mb-4 text-blue-400">Commodity Trading</h2>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-slate-600">
                    <th class="text-left py-2 px-3 text-blue-400 font-medium">COMMODITY</th>
                    <th class="text-left py-2 px-3 text-blue-400 font-medium">WEIGHT</th>
                    <th class="text-right py-2 px-3 text-blue-400 font-medium">BUY</th>
                    <th class="text-right py-2 px-3 text-blue-400 font-medium">SELL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="commodity in commodities"
                    :key="commodity.id"
                    class="border-b border-slate-700 hover:bg-slate-700/50"
                  >
                    <td class="py-3 px-3 font-medium">{{ commodity.name }}</td>
                    <td class="py-3 px-3 text-slate-300">{{ commodity.weight }}</td>
                    <td class="py-3 px-3 text-right font-mono">{{ formatPrice(commodity.buyPrice) }}</td>
                    <td class="py-3 px-3 text-right font-mono">{{ formatPrice(commodity.sellPrice) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Live Prices Panel (Right Panel) -->
        <div class="space-y-4">
          <!-- Gold OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-sm font-medium text-blue-400 mb-3">Gold OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-2xl font-bold text-red-400">3310.77</span>
                <span class="text-2xl font-bold text-red-400">3312.95</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-3 w-3" />
                  +3348.57
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-3 w-3" />
                  -3299.65
                </span>
              </div>
            </div>
          </div>

          <!-- Silver OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-sm font-medium text-blue-400 mb-3">Silver OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-xl font-bold">33.585</span>
                <span class="text-xl font-bold">33.558</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-3 w-3" />
                  +33.502
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-3 w-3" />
                  -32.869
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer Ticker -->
    <footer class="bg-slate-800 border-t border-slate-700 py-2">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between text-sm">
          <div class="flex items-center gap-2">
            <span class="text-blue-400">Gold Price News:</span>
            <span class="text-slate-300">Borrowing to Trade Gold</span>
          </div>
          <span class="text-slate-400">www.bullionstats.com</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Mock data - in real app this would come from API/store
const commodities = ref([
  {
    id: '1',
    name: 'GOLD 995',
    weight: '1KG',
    buyPrice: 387943,
    sellPrice: 389962
  },
  {
    id: '2',
    name: 'GOLD 22K',
    weight: '1KG',
    buyPrice: 358701,
    sellPrice: 360568
  },
  {
    id: '3',
    name: '1 GM',
    weight: '1GM',
    buyPrice: 388.71,
    sellPrice: 393.69
  },
  {
    id: '4',
    name: 'GOLD TEN TOLA',
    weight: '1TTB',
    buyPrice: 45500,
    sellPrice: 45668
  },
  {
    id: '5',
    name: 'SILVER',
    weight: '1KG',
    buyPrice: 3727.69,
    sellPrice: 4203.36
  }
])

// Real-time date and time
const currentDateTime = ref(new Date())

const formattedDate = computed(() => {
  return currentDateTime.value.toLocaleDateString('en-US', {
    weekday: 'long',
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  }).toUpperCase()
})

const formattedTime = computed(() => {
  return currentDateTime.value.toLocaleTimeString('en-US', {
    hour12: true,
    hour: '2-digit',
    minute: '2-digit'
  })
})

// Update time every second
onMounted(() => {
  const timer = setInterval(() => {
    currentDateTime.value = new Date()
  }, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})

// Helper function to format prices
const formatPrice = (price: number): string => {
  if (price >= 1000) {
    return price.toLocaleString('en-IN')
  }
  return price.toFixed(2)
}
</script>
