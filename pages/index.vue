<template>
  <div class="h-screen bg-slate-900 text-white flex flex-col overflow-hidden">
    <!-- Header Section -->
    <header class="bg-slate-900 border-b border-slate-700 flex-shrink-0">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <!-- Company branding and status -->
          <div class="flex items-center gap-6">
            <div class="flex items-center gap-4">
              <Icon name="lucide:coins" class="h-10 w-10 text-blue-400" />
              <h1 class="text-4xl font-bold text-white">SARCO JEWELLERY</h1>
            </div>
            <div class="h-8 w-[1px] bg-slate-700" />
            <span class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-lg font-medium bg-green-900 text-green-100">
              <Icon name="lucide:check-circle" class="h-5 w-5" />
              Market Open
            </span>
          </div>

          <!-- Date and time display -->
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-3">
              <Icon name="lucide:clock" class="h-6 w-6 text-slate-400" />
              <span class="font-medium text-white text-2xl">{{ formattedTime }}</span>
            </div>
            <div class="h-6 w-[1px] bg-slate-700" />
            <span class="text-xl text-slate-300">{{ formattedDate }}</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 px-6 py-4 min-h-0">
      <div class="h-full grid grid-cols-12 gap-6">
        <!-- Commodity Trading Table (Left Panel) -->
        <div class="col-span-8">
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-6 h-full">
            <h2 class="text-3xl font-semibold mb-4 text-blue-400">Commodity Trading</h2>
            <div class="h-full">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-slate-600">
                    <th class="text-left py-3 px-4 text-blue-400 font-medium text-xl">COMMODITY</th>
                    <th class="text-left py-3 px-4 text-blue-400 font-medium text-xl">WEIGHT</th>
                    <th class="text-right py-3 px-4 text-blue-400 font-medium text-xl">BUY</th>
                    <th class="text-right py-3 px-4 text-blue-400 font-medium text-xl">SELL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="commodity in topCommodities"
                    :key="commodity.id"
                    class="border-b border-slate-700"
                  >
                    <td class="py-4 px-4 font-medium text-lg">{{ commodity.name }}</td>
                    <td class="py-4 px-4 text-slate-300 text-lg">{{ commodity.weight }}</td>
                    <td class="py-4 px-4 text-right font-mono text-lg">{{ formatPrice(commodity.buyPrice) }}</td>
                    <td class="py-4 px-4 text-right font-mono text-lg">{{ formatPrice(commodity.sellPrice) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Live Prices Panel (Right Panel) -->
        <div class="col-span-4 space-y-4">
          <!-- Market Summary -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-xl font-medium text-blue-400 mb-3">Market Summary</h3>
            <div class="grid grid-cols-2 gap-3">
              <div class="text-center">
                <div class="text-lg text-slate-300">Gold</div>
                <div class="text-2xl font-bold text-yellow-400">₹3310.77</div>
                <div class="text-sm text-green-400">+1.2%</div>
              </div>
              <div class="text-center">
                <div class="text-lg text-slate-300">Silver</div>
                <div class="text-2xl font-bold text-slate-300">33.585</div>
                <div class="text-sm text-red-400">-0.5%</div>
              </div>
            </div>
          </div>

          <!-- Gold OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-xl font-medium text-blue-400 mb-3">Gold OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-3xl font-bold text-yellow-400">3310.77</span>
                <span class="text-3xl font-bold text-yellow-400">3312.95</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-4 w-4" />
                  +3348.57
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-4 w-4" />
                  -3299.65
                </span>
              </div>
            </div>
          </div>

          <!-- Silver OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-4">
            <h3 class="text-xl font-medium text-blue-400 mb-3">Silver OZ</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-3xl font-bold text-slate-300">33.585</span>
                <span class="text-3xl font-bold text-slate-300">33.558</span>
              </div>
              <div class="flex justify-between items-center text-sm">
                <span class="flex items-center gap-1 text-green-400">
                  <Icon name="lucide:trending-up" class="h-4 w-4" />
                  +33.502
                </span>
                <span class="flex items-center gap-1 text-red-400">
                  <Icon name="lucide:trending-down" class="h-4 w-4" />
                  -32.869
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer Ticker -->
    <footer class="bg-slate-800 border-t border-slate-700 py-3 flex-shrink-0">
      <div class="px-6">
        <div class="flex items-center justify-between text-lg">
          <div class="flex items-center gap-4">
            <span class="text-blue-400 font-medium">Gold Price News:</span>
            <span class="text-slate-300">Borrowing to Trade Gold - Market Analysis</span>
          </div>
          <span class="text-slate-400">www.bullionstats.com</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Mock data optimized for single-screen TV display
const commodities = ref([
  {
    id: '1',
    name: 'GOLD 995',
    weight: '1KG',
    buyPrice: 387943,
    sellPrice: 389962,
  },
  {
    id: '2',
    name: 'GOLD 22K',
    weight: '1KG',
    buyPrice: 358701,
    sellPrice: 360568,
  },
  {
    id: '3',
    name: 'GOLD 1GM',
    weight: '1GM',
    buyPrice: 388.71,
    sellPrice: 393.69,
  },
  {
    id: '4',
    name: 'SILVER',
    weight: '1KG',
    buyPrice: 3727.69,
    sellPrice: 4203.36,
  },
])

// Display only top 4 commodities to fit on single screen
const topCommodities = computed(() => commodities.value.slice(0, 4))

// Real-time date and time
const currentDateTime = ref(new Date())

const formattedDate = computed(() => {
  return currentDateTime.value
    .toLocaleDateString('en-US', {
      weekday: 'long',
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    })
    .toUpperCase()
})

const formattedTime = computed(() => {
  return currentDateTime.value.toLocaleTimeString('en-US', {
    hour12: true,
    hour: '2-digit',
    minute: '2-digit',
  })
})

// Update time every second
onMounted(() => {
  const timer = setInterval(() => {
    currentDateTime.value = new Date()
  }, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})

// Helper function to format prices
const formatPrice = (price: number): string => {
  if (price >= 1000) {
    return price.toLocaleString('en-IN')
  }
  return price.toFixed(2)
}
</script>
