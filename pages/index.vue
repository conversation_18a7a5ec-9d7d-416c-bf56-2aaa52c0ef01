<template>
  <div class="min-h-screen bg-slate-900 text-white">
    <!-- Header Section -->
    <header class="bg-slate-900 border-b border-slate-700">
      <div class="container mx-auto px-4 py-3">
        <div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
          <!-- Company branding -->
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-3">
              <Icon name="lucide:coins" class="h-8 w-8 text-blue-400" />
              <h1 class="text-3xl font-bold text-white">SARCO JEWELLERY</h1>
            </div>
            <div class="h-6 w-[1px] bg-slate-700 mx-2 hidden md:block" />
            <div class="hidden md:block">
              <span class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                <Icon name="lucide:check-circle" class="h-4 w-4" />
                Market Open
              </span>
            </div>
          </div>

          <!-- Date and time display -->
          <div class="flex items-center gap-3 text-lg text-slate-300">
            <div class="flex items-center gap-2">
              <Icon name="lucide:clock" class="h-5 w-5 text-slate-400" />
              <span class="font-medium text-white text-xl">{{ formattedTime }}</span>
            </div>
            <div class="h-6 w-[1px] bg-slate-700" />
            <span class="text-lg">{{ formattedDate }}</span>
          </div>
        </div>

        <!-- Market indicators -->
        <div class="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2 md:mt-3">
          <div class="flex items-center justify-between rounded-md border border-slate-600 bg-slate-800/50 px-4 py-3">
            <span class="text-lg font-medium text-slate-200">Gold</span>
            <div class="flex items-center gap-3">
              <span class="font-semibold text-white text-xl">₹3310.77</span>
              <span class="flex items-center text-sm font-medium text-green-400">
                <Icon name="lucide:trending-up" class="h-4 w-4 mr-1" />
                +1.2%
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between rounded-md border border-slate-600 bg-slate-800/50 px-4 py-3">
            <span class="text-lg font-medium text-slate-200">Silver</span>
            <div class="flex items-center gap-3">
              <span class="font-semibold text-white text-xl">33.585</span>
              <span class="flex items-center text-sm font-medium text-red-400">
                <Icon name="lucide:trending-down" class="h-4 w-4 mr-1" />
                -0.5%
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Commodity Trading Table (Left Panel) -->
        <div class="lg:col-span-2">
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h2 class="text-2xl font-semibold mb-6 text-blue-400">Commodity Trading</h2>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-slate-600">
                    <th class="text-left py-4 px-4 text-blue-400 font-medium text-lg">COMMODITY</th>
                    <th class="text-left py-4 px-4 text-blue-400 font-medium text-lg">WEIGHT</th>
                    <th class="text-right py-4 px-4 text-blue-400 font-medium text-lg">BUY</th>
                    <th class="text-right py-4 px-4 text-blue-400 font-medium text-lg">SELL</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="commodity in commodities"
                    :key="commodity.id"
                    class="border-b border-slate-700 hover:bg-slate-700/50"
                  >
                    <td class="py-4 px-4 font-medium text-lg">{{ commodity.name }}</td>
                    <td class="py-4 px-4 text-slate-300 text-lg">{{ commodity.weight }}</td>
                    <td class="py-4 px-4 text-right font-mono text-lg">{{ formatPrice(commodity.buyPrice) }}</td>
                    <td class="py-4 px-4 text-right font-mono text-lg">{{ formatPrice(commodity.sellPrice) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Live Prices Panel (Right Panel) -->
        <div class="space-y-6">
          <!-- Gold OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h3 class="text-xl font-medium text-blue-400 mb-4">Gold OZ</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-3xl font-bold text-red-400">3310.77</span>
                <span class="text-3xl font-bold text-red-400">3312.95</span>
              </div>
              <div class="flex justify-between items-center text-lg">
                <span class="flex items-center gap-2 text-green-400">
                  <Icon name="lucide:trending-up" class="h-4 w-4" />
                  +3348.57
                </span>
                <span class="flex items-center gap-2 text-red-400">
                  <Icon name="lucide:trending-down" class="h-4 w-4" />
                  -3299.65
                </span>
              </div>
            </div>
          </div>

          <!-- Silver OZ Section -->
          <div class="bg-slate-800 rounded-lg border border-slate-700 p-6">
            <h3 class="text-xl font-medium text-blue-400 mb-4">Silver OZ</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-3xl font-bold">33.585</span>
                <span class="text-3xl font-bold">33.558</span>
              </div>
              <div class="flex justify-between items-center text-lg">
                <span class="flex items-center gap-2 text-green-400">
                  <Icon name="lucide:trending-up" class="h-4 w-4" />
                  +33.502
                </span>
                <span class="flex items-center gap-2 text-red-400">
                  <Icon name="lucide:trending-down" class="h-4 w-4" />
                  -32.869
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer Ticker -->
    <footer class="bg-slate-800 border-t border-slate-700 py-4">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between text-lg">
          <div class="flex items-center gap-3">
            <span class="text-blue-400 font-medium">Gold Price News:</span>
            <span class="text-slate-300">Borrowing to Trade Gold</span>
          </div>
          <span class="text-slate-400">www.bullionstats.com</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Mock data - in real app this would come from API/store
const commodities = ref([
  {
    id: '1',
    name: 'GOLD 995',
    weight: '1KG',
    buyPrice: 387943,
    sellPrice: 389962
  },
  {
    id: '2',
    name: 'GOLD 22K',
    weight: '1KG',
    buyPrice: 358701,
    sellPrice: 360568
  },
  {
    id: '3',
    name: '1 GM',
    weight: '1GM',
    buyPrice: 388.71,
    sellPrice: 393.69
  },
  {
    id: '4',
    name: 'GOLD TEN TOLA',
    weight: '1TTB',
    buyPrice: 45500,
    sellPrice: 45668
  },
  {
    id: '5',
    name: 'SILVER',
    weight: '1KG',
    buyPrice: 3727.69,
    sellPrice: 4203.36
  }
])

// Real-time date and time
const currentDateTime = ref(new Date())

const formattedDate = computed(() => {
  return currentDateTime.value.toLocaleDateString('en-US', {
    weekday: 'long',
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  }).toUpperCase()
})

const formattedTime = computed(() => {
  return currentDateTime.value.toLocaleTimeString('en-US', {
    hour12: true,
    hour: '2-digit',
    minute: '2-digit'
  })
})

// Update time every second
onMounted(() => {
  const timer = setInterval(() => {
    currentDateTime.value = new Date()
  }, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})

// Helper function to format prices
const formatPrice = (price: number): string => {
  if (price >= 1000) {
    return price.toLocaleString('en-IN')
  }
  return price.toFixed(2)
}
</script>
