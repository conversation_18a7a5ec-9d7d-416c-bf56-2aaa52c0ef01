@import "tailwindcss";

:root {
  --background: 15 23 42; /* slate-900 */
  --foreground: 248 250 252; /* slate-50 */
  --primary: 59 130 246; /* blue-500 */
  --primary-foreground: 248 250 252; /* slate-50 */
  --secondary: 30 41 59; /* slate-800 */
  --secondary-foreground: 248 250 252; /* slate-50 */
  --accent: 51 65 85; /* slate-700 */
  --accent-foreground: 248 250 252; /* slate-50 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 248 250 252; /* slate-50 */
  --border: 51 65 85; /* slate-700 */
  --input: 30 41 59; /* slate-800 */
  --ring: 59 130 246; /* blue-500 */
  --success: 34 197 94; /* green-500 */
  --warning: 245 158 11; /* amber-500 */
}

* {
  border-color: rgb(51 65 85); /* slate-700 */
}

body {
  background-color: rgb(15 23 42); /* slate-900 */
  color: rgb(248 250 252); /* slate-50 */
  font-feature-settings: "rlig" 1, "calt" 1;
}

.container {
  width: 100%;
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: rgb(30 41 59); /* slate-800 */
}

::-webkit-scrollbar-thumb {
  background-color: rgb(71 85 105); /* slate-600 */
  border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(100 116 139); /* slate-500 */
}

/* Price animation classes */
.price-flash-up {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(34 197 94 / 0.2); /* green-500/20 */
}

.price-flash-down {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(239 68 68 / 0.2); /* red-500/20 */
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.ticker-animation {
  animation: ticker 30s linear infinite;
}
