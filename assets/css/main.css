@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 15 23 42; /* slate-900 */
    --foreground: 248 250 252; /* slate-50 */
    --primary: 59 130 246; /* blue-500 */
    --primary-foreground: 248 250 252; /* slate-50 */
    --secondary: 30 41 59; /* slate-800 */
    --secondary-foreground: 248 250 252; /* slate-50 */
    --accent: 51 65 85; /* slate-700 */
    --accent-foreground: 248 250 252; /* slate-50 */
    --destructive: 239 68 68; /* red-500 */
    --destructive-foreground: 248 250 252; /* slate-50 */
    --border: 51 65 85; /* slate-700 */
    --input: 30 41 59; /* slate-800 */
    --ring: 59 130 246; /* blue-500 */
    --success: 34 197 94; /* green-500 */
    --warning: 245 158 11; /* amber-500 */
  }
}

@layer base {
  * {
    @apply border-slate-700;
  }
  
  body {
    @apply bg-slate-900 text-slate-50;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Price animation classes */
.price-flash-up {
  @apply animate-pulse bg-green-500/20;
}

.price-flash-down {
  @apply animate-pulse bg-red-500/20;
}

/* Ticker animation */
@keyframes ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.ticker-animation {
  animation: ticker 30s linear infinite;
}
