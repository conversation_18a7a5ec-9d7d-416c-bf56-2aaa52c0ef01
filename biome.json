{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "suspicious": {"noExplicitAny": "warn"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded"}}, "json": {"formatter": {"trailingCommas": "none"}}, "files": {"include": ["**/*.js", "**/*.ts", "**/*.vue", "**/*.json"], "ignore": ["node_modules/**", ".nuxt/**", ".output/**", "dist/**", "coverage/**"]}}