{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "typecheck": "nuxt typecheck"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/tailwindcss": "^7.0.0-beta.0", "@pinia/nuxt": "^0.11.0", "@vueuse/core": "^13.3.0", "@vueuse/nuxt": "^13.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.511.0", "nuxt": "^3.17.4", "pinia": "^3.0.2", "tailwind-merge": "^3.3.0", "vue": "^3.5.15", "vue-router": "^4.5.1"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@iconify-json/lucide": "^1.2.45", "lefthook": "^1.11.13", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}}